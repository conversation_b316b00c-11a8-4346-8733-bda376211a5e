<template>
  <div class="login-wrap">
    <div class="login-logo">
      <NuxtLink to="/">
        <img src="@/assets/images/login/login-logo.png" alt="">
      </NuxtLink>
    </div>
    <div class="login-form-wrap">
      <ul class="login-type-list">
        <li v-for="(item,index) in loginTypeList" :key="index" class="login-type-item" :class="{actived: currentLoginType == index}" @click="changeLoginType(index)">
          {{ item }}
        </li>
      </ul>
      <el-form ref="loginRef" :model="loginForm" :rules="formRules" class="login-form" :validate-on-rule-change="false">
        <LoginAccountFormItem v-show="currentLoginType == 0" v-model:loginForm="loginForm" @handleLogin="handleLogin"/>
        <LoginMobileFormItem v-show="currentLoginType == 1" v-model:loginForm="loginForm" :loginRef="loginRef" @handleLogin="handleLogin" @handleVerify="handleVerify" />
        <el-form-item class="login-btn" style="width:100%;">
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            style="width:100%;"
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
        <div class="tip-wrap">
          <NuxtLink to="/register" class="register">立即注册</NuxtLink>
          <NuxtLink to="/resetPassword" class="reset">忘记密码？</NuxtLink>
        </div>
      </el-form>
    </div>
    <LoginFooter class="login-footer"/>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from '@/store/userStore';
import type { FormInstance } from 'element-plus';

definePageMeta({
  middleware: ['auth'],
  layout: 'login',
})

const loginRef = ref<FormInstance>();
const currentLoginType = ref(0);
const loading = ref(false);

const loginForm = ref({
  username: "",
  password: "",
  phonenumber: "",
  smsCode: ""
});
const loginTypeList = ['账号密码登录','验证码登录']

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const redirect = ref(route.query.redirect);

const loginRules = {
  username: [
    { required: true, trigger: "blur", message: "请输入账户名称" },
    { pattern: userNamePattern, trigger: "blur", message: "请输入有效登录账户" }
  ],
  password: [
    { required: true, trigger: "blur", message: "请输入登录密码" },
    { pattern: passwordPattern, trigger: "blur", message: "请输入有效登录密码" }
  ],
};
const mobileRules = {
  phonenumber: [
    { required: true, trigger: "blur", message: "请输入手机号码" },
    { pattern: mobileValidPattern, trigger: "blur", message: "请输入正确的手机号码" }
  ],
  smsCode: [{ required: true, trigger: "blur", message: "请输入验证码" }],
}
const formRules = computed(() => {
  return currentLoginType.value == 0 ? loginRules : mobileRules;
})

const changeLoginType = (index: number) => {
  currentLoginType.value = index;
}
const handleVerify = () => {
  
}
const handleLogin = () => {
  loginRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      let path = typeof redirect.value == 'string' ? redirect.value : '/'
      if(currentLoginType.value == 0){ // 账号登录
        userStore.login(loginForm.value).then(res =>{
          router.push({ path: path });
        }).catch((error)=>{
          loading.value = false;
        })
      }else{ // 手机登录
        userStore.mobileLogin(loginForm.value).then(res =>{
          router.push({ path: path });
        }).catch((error)=>{
          loading.value = false;
        })
      }
    }
  })
}
</script>
<style lang="scss" scoped>
.login-wrap{
  height: 100vh;
  background: url('@/assets/images/login/login-bg.png') no-repeat center;
  background-size: 100% 100%;
  position: relative;
  .login-logo{
    position: absolute;
    top: 70px;
    left: 70px;
    img{
      height: 68px;
    }
  }
  .login-form-wrap{
    position: absolute;
    top: calc(50% - 270px);
    right: 15%;
    width: 480px;
    height: 540px;
    background: #FFFFFF;
    box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.16);
    border-radius: 8px;
    box-sizing: border-box;
    padding: 40px;
    .login-type-list{
      margin-top: 40px;
      display: flex;
      justify-content: center;
      box-sizing: border-box;
      gap: 60px;
      font-size: 22px;
      .login-type-item{
        font-size: 22px;
        color: #333333;
        cursor: pointer;
        &.actived{
          font-weight: bold;
          color: $primary-color;
          position: relative;
          &::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 35px;
            transform: translateX(-50%); /* 使伪元素水平居中 */
            width: 50%; /* 自定义长度的一半 */
            height: 5px; /* 自定义border-bottom的厚度 */
            background: $primary-color; /* 自定义border-bottom的颜色 */
            border-radius: 3px;
          }
        }
        
      }
      
    }
    .login-form{
      margin-top: 60px;
      .login-btn{
        margin-top: 70px;
      }  
      .tip-wrap{
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        font-size: 14px;
        a{
          color: #999999;
          &:hover{
            color: $primary-color;
          }
        }
       .register{
          color: $primary-color;
          text-decoration: underline;
        }

      }
    }
    :deep(.el-form-item) {
      width: 100%;
      height: 48px;
      line-height: 48px;
      border: none;
      margin-right: 0px !important;
      .el-input{
        height: 48px;
        line-height: 48px;
        border: none;
        
        font-size: 14px;
      }
      .el-input__wrapper,.el-input__inner{
        background: #F6F7F9;
        border: none;
        box-shadow: none;
        border-radius: 3px;
      }
      .el-input__prefix{
        margin-left: 5px;
        // width: 30px;
        .p-num{
          display: flex;
          align-items: center;
          span{
            font-size: 16px;
            color: #333333;
          }
          .line{
            width: 1px;
            height: 28px;
            background: #DCDCDC;
            margin: 0 10px;
          }
        }
      }
      .el-input-group__append{
        padding: 0px;
        .verify{
          padding: 0 12px;
          text-align: center;
          background: $primary-color;
          font-size: 14px;
          color: #ffffff;
          cursor: pointer;
        }
        .down-timer{
          padding: 0 12px;
          text-align: center;
          background: #89b3ff;
          font-size: 14px;
          color: #ffffff;
        }
      }
      
      .el-button{
        height: 48px;
        font-size: 18px;
        background: $primary-color;
        border-radius: 3px;
        font-weight: bold;
        border-color: $primary-color !important;
      }
    }
  }
  .login-footer{
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
  }
}
</style>