<template>
  <el-dialog
    width="1330"
    class="pay"
    v-model="props.visible"
    :append-to-body="true"
    :lock-scroll="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    @close="handleClose"
  >
    <template #header="{ close }">
      <img @click="close" src="@/assets/images/vip/close.png" alt="" class="pay-close" />
      <div class="pay-content">
        <div class="pay-trapezoid">
          <div @click="handleTab(item.type)" v-for="item in tabList" :key="item.title + item.type" class="pay-trapezoid-item">
            <img :src="packageType == item.type ? trapezoidActive : item.img" alt="" class="pay-trapezoid-item-img" />
            <div class="pay-trapezoid-item-title" :class="{ 'c-primary': packageType == item.type }">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </template>
    <div class="flex" :loading="loading">
      <div class="items">
        <div class="h-title f-20">服务项目</div>
        <div class="items-content" :class="{ 'p-0-25': servicesAvailable.length > 0 }">
          <template v-if="servicesAvailable && servicesAvailable.length > 0">
            <div v-for="(item, index) in servicesAvailable" :key="index" class="items-content-item">
              <div>{{ item.name }}</div>
              <img v-if="item.number == '有'" src="@/assets/images/vip/none.png" alt="" class="items-content-icon" />
              <img v-else-if="item.number == '无'" src="@/assets/images/vip/have.png" alt="" class="items-content-icon" />
              <span v-else>{{ item.number }}</span>
            </div>
          </template>
          <BxcEmpty v-else />
        </div>
      </div>
      <div class="package">
        <div class="flex flex-sb flex-ai-center">
          <div class="f-18 c-33 f-bold">购买账号：{{ userStore.nickName }}</div>
          <div @click="handleService" class="f-14 c-33 flex flex-ai-center pointer">
            联系客服
            <img src="@/assets/images/vip/service.png" alt="" class="package-service ml-5" />
          </div>
        </div>
        <div v-show="packageType == 3 && benefitPackage.length > 0" class="package-benefit">
          <div
            @click="handleBenefitPackage(index)"
            v-for="(item, index) in benefitPackage"
            :key="item.id"
            class="package-benefit-item"
            :class="{ 'is-active': activeBenefitPackage == index }"
          >
            {{ item.title }}-体验优质服务
          </div>
        </div>
        <div class="package-content">
          <template v-if="memberPackage.length > 0">
            <div
              @click="handlePackage(index)"
              v-for="(item, index) in memberPackage"
              :key="item.id"
              class="package-content-item"
              :style="{ backgroundImage: `url(${item.type == 'annual' ? annual : monthly})` }"
              :class="{
                'is-annual-active': item.type == 'annual' && activePackage == index,
                'is-monthly-active': item.type == 'monthly' && activePackage == index,
              }"
            >
              <div>
                <div>
                  <span class="f-36 c-primary f-bold">{{ item.originalPrice - item.discount }}</span>
                  /{{ item.type == 'annual' ? '年' : '月' }}
                </div>
                <div class="mt-5">
                  原价：
                  <span class="line-through">{{ item.originalPrice }}/{{ item.type == 'annual' ? '年' : '月' }}</span>
                </div>
              </div>
              <div v-if="item.type == 'annual'" class="f-14 c-ff package-content-item-price">
                每月{{ formatDecimal((item.originalPrice - item.discount) / 12) }}/元
              </div>
            </div>
          </template>
          <BxcEmpty v-else />
        </div>
        <template v-if="memberPackage && memberPackage.length > 0">
          <div class="f-18 c-33 f-bold mt-40">付款方式</div>
          <div class="flex flex-ai-center mt-20">
            <div
              @click="handlePayment(item.type)"
              v-for="item in paymentList"
              :key="item.type"
              class="package-btn"
              :class="{ 'is-active': payment == item.type }"
            >
              <img :src="item.img" alt="" class="package-btn-img" />
              {{ item.title }}
            </div>
          </div>
        </template>
        <div v-show="packageType == 1 || packageType == 2">
          <div class="f-18 c-33 f-bold mt-40">会员须知：</div>
          <div class="f-14 c-33 f-bold mt-20">会员有效期：</div>
          <div class="f-14 c-33 mt-10 lh-24">
            会员有效期根据您选择的会员类型（如月度会员、年度会员等）来确定。若您选择的是月度会员，那么您的会员资格将在开通后的一个月内有效；若是年度会员，则有效期为一年。会员不可叠加购买。
          </div>
          <div class="f-14 c-33 f-bold mt-20">会员升级：</div>
          <div class="f-14 c-33 mt-10 lh-24">若您需要将个人会员升级为企业会员或会员等级升级，请联系客服或拨打电话400-109-7887。</div>
          <div class="f-14 c-33 c-ff0000 lh-24">注：会员一经购买，概不退款。会员权益如未使用，逾期作废。</div>
        </div>
        <div v-show="packageType == 3">
          <div class="f-18 c-33 f-bold mt-40">权益包内容须知：</div>
          <div class="f-14 c-33 f-bold mt-20">权益包有效期：</div>
          <div class="f-14 c-33 mt-10 lh-24">
            权益包有效期和会员有效期不共享。权益包存在单独有效期，例如：您的会员购买起止时间为：2025年1月1日至2025年12月31止。期间5月1日购买权益包，则权益包服务的时间从5月1日计算。
          </div>
          <div class="f-14 c-33 f-bold mt-20">权益包叠加购买：</div>
          <div class="f-14 c-33 mt-10 lh-24">
            权益包支持叠加购买，在权益包服务时间内使用即可。注：权益包一经购买，概不退款。权益包如未使用，逾期作废。
          </div>
        </div>
      </div>
      <div class="code" :class="{ 'p-30-25': memberPackage.length > 0 }">
        <template v-if="memberPackage.length > 0">
          <div class="code-content">
            <div class="c-ff0000">
              <span class="f-18">￥</span>
              <span class="f-36 f-bold">
                {{ formatDecimal(memberPackage[activePackage].originalPrice - memberPackage[activePackage].discount) || 0 }}
              </span>
            </div>
            <div class="f-14 c-99 mt-15">
              原价：
              <span class="line-through">
                {{ formatDecimal(memberPackage[activePackage].originalPrice) || 0 }}/{{
                  memberPackage[activePackage].type == 'annual' ? '年' : '月'
                }}
              </span>
            </div>
            <div class="code-content-qr" :class="{ 'code-content-mask': !userStore.token }">
              <div v-if="!userStore.token" class="code-content-qr-text">
                请
                <span @click="handleLogin" class="c-primary text-underline pointer">登录</span>
                后下单
              </div>
              <img :src="qrCode" alt="" class="code-content-qr-img" />
            </div>
          </div>
          <div class="f-12 c-33 mt-20">
            支付完成就代表同意
            <a href="/file/标信查付费会员服务协议.html" target="_blank" class="c-primary pointer">《标信查交易服务协议》</a>
          </div>
        </template>
        <BxcEmpty v-else />
      </div>
    </div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="getPayQrCode" />
  </el-dialog>
  <ClientOnly>
    <CscPopSelectService v-if="openService" v-model:open="openService" :list="customerList" @success="selectServiceSuccess" />
    <CscPopOnlineConsult v-if="open" v-model:open="open" :customer="currentCustomer" />
  </ClientOnly>
</template>

<script setup lang="ts">
  import trapezoidOne from '@/assets/images/vip/trapezoid-one.png';
  import trapezoidTwo from '@/assets/images/vip/trapezoid-two.png';
  import trapezoidActive from '@/assets/images/vip/trapezoid-active.png';
  import zfb from '@/assets/images/vip/zfb.png';
  import wx from '@/assets/images/vip/wx.png';
  import annual from '@/assets/images/vip/annual.png';
  import monthly from '@/assets/images/vip/monthly.png';
  import qrcode_login from '@/assets/images/vip/qrcode_login.png';
  import type { ICustomer, IMemberPackage, IBenefitPackageItem, IBenefitPackage, IServiceItem } from '@/types/index';
  import { useUserStore } from '@/store/userStore';
  import { getCustomerList } from '@/api/online-service';
  import { getQrCode, getPayOrder } from '@/api/vip';
  import * as QRCode from 'qrcode';

  const userStore = useUserStore();
  const emit = defineEmits(['update:visible', 'success']);
  const { $modal } = useNuxtApp();

  interface IPayDialogProps {
    visible: boolean;
    selectItem: IMemberPackage | IBenefitPackageItem;
  }

  const props = withDefaults(defineProps<IPayDialogProps>(), {
    visible: false,
    selectItem: () => ({
      id: 0,
      originalPrice: 0,
      discount: 0,
      type: 'annual',
      package: [],
    }),
  });

  const { selectItem } = toRefs(props);

  const timer = ref<any>(null);
  const vipId = ref<number | string>(0);
  const qrCode = ref(qrcode_login);
  const orderId = ref('');
  const loading = ref(false);
  const open = ref(false);
  const openService = ref(false);
  const customerList = ref(<ICustomer[]>[]);
  const currentCustomer = ref(<ICustomer>{});
  const openLogin = ref<boolean>(false);
  const packageType = ref<number>(1);
  const tabList = ref([
    {
      type: 1,
      title: '个人会员',
      img: trapezoidOne,
    },
    {
      type: 2,
      title: '企业会员',
      img: trapezoidTwo,
    },
    {
      type: 3,
      title: '权益包',
      img: trapezoidOne,
    },
  ]);
  const activePackage = ref<number>(0);
  const servicesAvailable = ref<IServiceItem[]>([]);
  const memberPackage = ref<IMemberPackage[] | IServiceItem[]>([]);
  const individualMember = ref<IMemberPackage[]>([
    {
      id: 2,
      originalPrice: 60,
      discount: 30,
      type: 'monthly',
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '10条', name: '标准托管' },
        { id: 105, number: '600次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '100页', name: '标准解析器' },
        { id: 110, number: '无', name: '数字标准编辑器' },
        { id: 111, number: '5个会话', name: '标准问答' },
        { id: 112, number: '1台', name: '多设备登录' },
        { id: 113, number: '无', name: '成员数量' },
      ],
    },
    {
      id: 3,
      originalPrice: 600,
      discount: 300,
      type: 'annual',
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '100条', name: '标准托管' },
        { id: 105, number: '3000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '1500页', name: '标准解析器' },
        { id: 110, number: '无', name: '数字标准编辑器' },
        { id: 111, number: '10个会话', name: '标准问答' },
        { id: 112, number: '1台', name: '多设备登录' },
        { id: 113, number: '无', name: '成员数量' },
      ],
    },
  ]);
  const corporateMember = ref<IMemberPackage[]>([
    {
      id: 4,
      originalPrice: 1200,
      discount: 400,
      type: 'annual',
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '200条', name: '标准托管' },
        { id: 105, number: '8000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '4000页', name: '标准解析器' },
        { id: 110, number: '2项目', name: '数字标准编辑器' },
        { id: 111, number: '10个会话', name: '标准问答' },
        { id: 112, number: '5台', name: '多设备登录' },
        { id: 113, number: '10个', name: '成员数量' },
      ],
    },
    {
      id: 5,
      originalPrice: 2400,
      discount: 400,
      type: 'annual',
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '300条', name: '标准托管' },
        { id: 105, number: '12000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '10000页', name: '标准解析器' },
        { id: 110, number: '5项目', name: '数字标准编辑器' },
        { id: 111, number: '20个会话', name: '标准问答' },
        { id: 112, number: '10台', name: '多设备登录' },
        { id: 113, number: '20个', name: '成员数量' },
      ],
    },
    {
      id: 6,
      originalPrice: 3600,
      discount: 400,
      type: 'annual',
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '500条', name: '标准托管' },
        { id: 105, number: '18000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '不限', name: '标准解析器' },
        { id: 110, number: '10项目', name: '数字标准编辑器' },
        { id: 111, number: '30个会话', name: '标准问答' },
        { id: 112, number: '20台', name: '多设备登录' },
        { id: 113, number: '30个', name: '成员数量' },
      ],
    },
  ]);
  const activeBenefitPackage = ref<number>(0);
  const benefitPackage = ref<IBenefitPackage[]>([
    {
      id: 30000,
      title: '标准查新',
      introduce: '标准查新权益包',
      package: [
        { id: 1, originalPrice: 60, discount: 40, indate: '1', type: 'annual', number: 600, name: '标准查新' },
        { id: 2, originalPrice: 70, discount: 40, indate: '1', type: 'annual', number: 1000, name: '标准查新' },
        { id: 3, originalPrice: 165, discount: 40, indate: '1', type: 'annual', number: 5000, name: '标准查新' },
        { id: 4, originalPrice: 239, discount: 40, indate: '1', type: 'annual', number: 10000, name: '标准查新' },
      ],
    },
  ]);
  const payment = ref<string>('0');
  const paymentList = ref([
    { title: '支付宝扫码支付', img: zfb, type: '0' },
    { title: '微信扫码支付', img: wx, type: '1' },
  ]);

  onMounted(() => {
    loading.value = true;
    packageType.value = Number(selectItem.value.packageType) || 1;
    switch (packageType.value) {
      case 1:
        vipId.value = selectItem.value.id;
        memberPackage.value = individualMember.value || [];
        memberPackage.value.forEach((item, index) => {
          if (item.id == selectItem.value.id) {
            activePackage.value = index;
            servicesAvailable.value = memberPackage.value[index]?.package || [];
          }
        });
        break;
      case 2:
        vipId.value = selectItem.value.id;
        memberPackage.value = corporateMember.value || [];
        memberPackage.value.forEach((item, index) => {
          if (item.id == selectItem.value.id) {
            activePackage.value = index;
            servicesAvailable.value = memberPackage.value[index]?.package || [];
          }
        });
        break;
      case 3:
        vipId.value = selectItem.value.childrenId;
        memberPackage.value = benefitPackage.value[0]?.package || [];
        benefitPackage.value.forEach((item: IBenefitPackage, index: number) => {
          if (item.id == selectItem.value.id) {
            activeBenefitPackage.value = index;
            item.package.forEach((childrenItem: IBenefitPackageItem, childrenIndex: number) => {
              if (childrenItem.id == selectItem.value.childrenId) {
                activePackage.value = childrenIndex;
                servicesAvailable.value = [{ ...childrenItem }];
              }
            });
          }
        });
        break;
      default:
        memberPackage.value = [];
        servicesAvailable.value = [];
        break;
    }
    getPayQrCode();
    loading.value = false;
  });

  const getPayQrCode = () => {
    if (userStore.token) {
      getQrCode({ id: vipId, packageType: packageType.value, paymentMethod: payment.value, orderId: orderId.value })
        .then(async (res: any) => {
          let data = res.data;
          qrCode.value = await QRCode.toDataURL(data.codeUrl);
          orderId.value = data.orderId;
          if (!timer.value) {
            timer.value = setInterval(() => {
              getPayOrder(orderId.value).then((res: any) => {
                if (res.message == 'SUCCESS') {
                  $modal.msgSuccess('支付成功');
                  userStore.getUserInfo();
                  handleClose();
                }
              });
            }, 3000);
          }
        })
        .catch((err: any) => {
          qrCode.value = qrcode_login;
        });
    }
  };

  const handleTab = (type: number) => {
    packageType.value = type;
    activePackage.value = 0;
    switch (Number(packageType.value)) {
      case 1:
        memberPackage.value = individualMember.value || [];
        servicesAvailable.value = memberPackage.value[0]?.package || [];
        break;
      case 2:
        memberPackage.value = corporateMember.value || [];
        servicesAvailable.value = memberPackage.value[0]?.package || [];
        break;
      case 3:
        memberPackage.value = benefitPackage.value[0]?.package || [];
        let arr = [{ ...memberPackage.value[0] }];
        servicesAvailable.value = arr || [];
        break;
      default:
        servicesAvailable.value = [];
        memberPackage.value = [];
        break;
    }

    if (memberPackage.value && memberPackage.value.length > 0) {
      vipId.value = memberPackage.value[0].id;
      getPayQrCode();
    }
  };

  const handlePackage = (index: number) => {
    activePackage.value = index;
    servicesAvailable.value = memberPackage.value[index]?.package || [{ ...memberPackage.value[index] }] || [];
    vipId.value = memberPackage.value[index].id;
    getPayQrCode();
  };

  const handleBenefitPackage = (index: number) => {
    activePackage.value = 0;
    activeBenefitPackage.value = index;
    memberPackage.value = benefitPackage.value[index]?.package || [];
    let arr = [{ ...benefitPackage.value[index]?.package[0] }];
    servicesAvailable.value = arr || [];
    vipId.value = memberPackage.value[index].id;
    getPayQrCode();
  };

  const handlePayment = (type: string) => {
    payment.value = type;
    getPayQrCode();
  };

  const handleService = () => {
    getCustomers();
  };

  const getCustomers = () => {
    getCustomerList().then((res: any) => {
      if (res.data && res.data.length > 0) {
        customerList.value = res.data;
        if (customerList.value.length > 1) {
          openService.value = true;
        } else {
          currentCustomer.value = customerList.value[0] || {};
          open.value = true;
        }
      } else {
        $modal.msgError('暂无客服在线');
      }
    });
  };

  const selectServiceSuccess = (item: ICustomer) => {
    currentCustomer.value = item;
    open.value = true;
    openService.value = false;
  };

  const handleLogin = () => {
    openLogin.value = true;
  };

  const handleClose = () => {
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
    }
    emit('update:visible', false);
  };
</script>

<style lang="scss">
  .pay {
    border-radius: 5px !important;
    overflow: hidden;

    .el-dialog__header {
      height: 89px !important;
      position: relative;
      padding: 0 !important;
      border: none !important;
      background-image: url('@/assets/images/vip/pay_bg.jpg');
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100%;
      background-color: red;
    }

    .el-dialog__body {
      padding: 0 !important;
      overflow: hidden !important;
      max-height: calc(100vh - 150px - 90px) !important;
    }
  }
</style>

<style lang="scss" scoped>
  .pay {
    &-close {
      position: absolute;
      top: 20px;
      right: 25px;
      width: 17px;
      height: 17px;
      z-index: 99;
      cursor: pointer;
    }

    &-content {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &-trapezoid {
      display: flex;
      justify-content: center;

      &-item {
        position: relative;
        height: 53px;
        line-height: 53px;
        width: 260px;
        background-size: 100% 100%;
        cursor: pointer;

        &:first-child {
          z-index: 10;
        }

        &:nth-child(2) {
          z-index: 5;
        }

        &:not(:first-child) {
          margin-left: -20px;
        }

        &-img {
          display: block;
          height: 53px;
        }

        &-title {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          font-size: 18px;
          font-weight: bold;
          color: #fff;
        }
      }
    }

    .items {
      height: 100%;
      width: 295px;
      background-color: #f8fbff;
      overflow: hidden;

      &-content {
        box-sizing: border-box;
        height: calc(100vh - 150px - 89px - 20px - 60px);
        overflow-y: scroll;

        &::-webkit-scrollbar {
          width: 7px !important;
          height: 7px !important;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #dedfe0 !important;
          border-radius: 10px !important;
        }

        &-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          color: #333;
          margin-bottom: 30px;
        }

        &-icon {
          width: 14px;
          height: 14px;
        }
      }
    }

    .package {
      flex: 1;
      margin-top: 30px;
      padding: 0 25px 30px;
      height: calc(100vh - 150px - 89px - 60px);
      overflow-y: scroll;

      &::-webkit-scrollbar {
        width: 7px !important;
        height: 7px !important;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dedfe0 !important;
        border-radius: 10px !important;
      }

      &-service {
        height: 24px;
      }

      &-content {
        display: flex;
        flex-wrap: wrap;

        &-item {
          width: 220px;
          height: 135px;
          width: calc((100% - 40px) / 3);
          margin: 20px 20px 0 0;
          border-radius: 5px;
          font-size: 14px;
          color: #999;
          background-repeat: no-repeat;
          background-position: center;
          background-size: 100% 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-align: center;
          cursor: pointer;

          &:nth-child(3n) {
            margin-right: 0;
          }

          &-price {
            position: relative;
            top: 22px;
          }
        }

        .is-annual-active {
          background-image: url('@/assets/images/vip/annual_active.png') !important;
        }

        .is-monthly-active {
          background-image: url('@/assets/images/vip/monthly_active.png') !important;
        }
      }

      &-benefit {
        display: flex;
        flex-wrap: wrap;

        &-item {
          font-size: 14px;
          color: #333;
          line-height: 24px;
          background: #ffffff;
          border-radius: 5px;
          border: 1px solid #e6e6e6;
          padding: 10px 15px;
          box-sizing: border-box;
          margin-top: 20px;
          cursor: pointer;

          &:nth-child(n + 1) {
            margin-right: 15px;
          }
        }

        .is-active {
          color: #fff !important;
          border-color: $primary-color !important;
          background-color: $primary-color !important;
          font-weight: bold;
        }
      }

      &-btn {
        width: 160px;
        height: 47px;
        background: #ffffff;
        border-radius: 5px;
        border: 1px solid #e6e6e6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #333;
        cursor: pointer;

        &:nth-child(n + 2) {
          margin-left: 20px;
        }

        &-img {
          height: 24px;
          margin-right: 10px;
        }
      }

      .is-active {
        color: $primary-color;
        border-color: $primary-color;
        font-weight: bold;
      }
    }

    .code {
      width: 295px;
      background: #f8fbff;
      box-sizing: border-box;

      &-content {
        text-align: center;
        background-color: #fff;
        border: 1px solid #ddecf8;
        padding: 60px 0 70px;
        box-sizing: border-box;

        &-qr {
          position: relative;
          width: 160px;
          height: 160px;
          margin: 25px auto 0;
          border-radius: 5px;
          overflow: hidden;

          &-img {
            width: 100%;
            height: 100%;
          }

          &-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            word-break: keep-all;
          }
        }

        &-mask {
          background: #dadada;
          opacity: 0.94;
        }
      }
    }
  }

  .h-title {
    position: relative;
    left: -10px;
    margin: 30px 25px;
  }

  .p-0-25 {
    padding: 0 25px;
  }

  .p-30-25 {
    padding: 30px 25px;
  }
</style>
